# 图元保护系统

## 概述

这是一个增强的AutoCAD图元保护系统，不仅在扩展字典中添加保护标记，还通过事件处理机制实现真正的编辑拦截功能。

## 主要功能

### 1. 真正的保护功能
- **编辑拦截**：阻止非保护用户修改受保护的图元
- **删除拦截**：阻止非保护用户删除受保护的图元
- **用户权限**：只有保护用户本人可以修改自己保护的图元
- **自动恢复**：被误删的受保护图元会自动恢复

### 2. 保护级别
- `Full`：完全保护（不能修改、不能删除）
- `ReadOnly`：只读保护（不能修改、不能删除）
- `Partial`：部分保护（允许某些操作）
- `DeleteOnly`：仅删除保护（不能删除，可以修改）

### 3. 自动初始化
- 插件加载时自动初始化保护系统
- 插件卸载时自动清理保护系统

## 可用命令

### 基础命令

1. **LockEntitiesDebugCmd** - 文档保护
   - 自动保护所有包含WellModel记录的BlockReference
   - 支持强制更新已保护的图元

2. **UnprotectAllWellsCommand** - 文档保护解锁
   - 解除所有WellModel图元的保护

3. **CheckProtectionStatusCmd** - 检查保护状态
   - 显示所有WellModel图元的保护状态详情

### 系统管理命令

4. **InitProtectionSystemCmd** - 初始化保护系统
   - 手动初始化保护系统（通常自动完成）

5. **CleanupProtectionSystemCmd** - 清理保护系统
   - 清理保护系统事件处理器

6. **TestProtectionCmd** - 测试保护功能
   - 显示测试步骤和当前用户信息

### 手动保护命令

7. **ProtectSelectedEntitiesCmd** - 选择保护图元
   - 手动选择要保护的图元

8. **UnprotectSelectedEntitiesCmd** - 选择解除保护
   - 手动选择要解除保护的图元

## 使用步骤

### 自动保护WellModel图元

1. 运行 `LockEntitiesDebugCmd` 命令
2. 系统会自动找到所有包含WellModel记录的BlockReference并保护它们
3. 尝试修改或删除这些图元，会看到保护警告

### 手动保护任意图元

1. 运行 `ProtectSelectedEntitiesCmd` 命令
2. 选择要保护的图元
3. 图元将被保护，无法被其他用户修改

### 测试保护功能

1. 运行 `TestProtectionCmd` 查看测试步骤
2. 按照提示步骤测试保护功能
3. 观察保护警告和拦截效果

## 技术实现

### 事件处理机制

系统通过注册以下事件来实现保护功能：

- `Database.ObjectModified`：拦截图元修改操作
- `Database.ObjectErased`：拦截图元删除操作
- `DocumentManager.DocumentActivated`：处理文档切换

### 保护数据存储

保护信息存储在图元的扩展字典中：

```
ExtensionDictionary
└── ENTITY_PROTECTION (DBDictionary)
    ├── PROTECTION_LEVEL (Xrecord) - 保护级别
    ├── PROTECTION_TIME (Xrecord)  - 保护时间
    └── PROTECTION_USER (Xrecord)  - 保护用户
```

### 权限检查

- 检查当前用户是否为保护用户
- 根据保护级别决定是否允许操作
- 显示相应的警告信息

## 注意事项

1. **用户权限**：只有保护用户本人可以修改自己保护的图元
2. **系统初始化**：保护系统会在插件加载时自动初始化
3. **事件处理**：保护功能依赖于事件处理机制，确保系统已正确初始化
4. **性能影响**：大量图元的保护检查可能影响性能
5. **数据持久性**：保护信息存储在图纸文件中，随文件保存

## 故障排除

### 保护不生效

1. 检查保护系统是否已初始化：运行 `InitProtectionSystemCmd`
2. 检查图元是否真的受保护：运行 `CheckProtectionStatusCmd`
3. 确认当前用户不是保护用户本人

### 无法解除保护

1. 确认当前用户是保护用户本人
2. 使用 `UnprotectSelectedEntitiesCmd` 手动解除保护
3. 检查图元的扩展字典是否完整

### 系统错误

1. 查看AutoCAD命令行的错误信息
2. 重新初始化保护系统
3. 检查图纸文件是否损坏

## 扩展开发

### 添加新的保护级别

在 `ProtectionLevel` 枚举中添加新级别，并在事件处理器中添加相应逻辑。

### 自定义保护规则

修改 `Database_ObjectModified` 和 `Database_ObjectErased` 方法中的保护逻辑。

### 集成其他业务模型

参考WellModel的实现，为其他业务模型添加保护功能。
