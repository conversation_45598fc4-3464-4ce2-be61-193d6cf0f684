using SmartRouting.App.Commands.数据录入.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;

namespace SmartRouting.App.Commands.权限管理
{
    #region 用户命令
    public class ProtectionCommands
    {
        [CommandTag("排    模", nameof(LockEntitiesDebugCmd), "文档保护")]
        [CommandMethod(nameof(LockEntitiesDebugCmd))]
        public static void LockEntitiesDebugCmd()
        {
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;
            string recordName = typeof(WellModel).Name;

            ed.WriteMessage($"\n开始安全保护模式，查找记录名: '{recordName}'");

            // 使用强制更新参数，确保保护系统已初始化
            int protectedCount = EntityProtectionManager.ProtectEntitiesByXRecord(
                recordName,
                EntityProtectionManager.ProtectionLevel.Full,
                null,
                forceUpdate: true);

            ed.WriteMessage($"\n命令执行完毕，自动保护了 {protectedCount} 个工井模型。");
            ed.WriteMessage($"\n提示：图元现在受到安全保护，系统会监控编辑操作！");
            ed.WriteMessage($"\n受保护的图元已变为深灰色，表示其受保护状态。");
            ed.WriteMessage($"\n注意：为避免崩溃，现在使用安全的命令监控模式。");
        }

        [CommandTag("排    模", nameof(UnprotectAllWellsCommand), "文档保护解锁")]
        [CommandMethod(nameof(UnprotectAllWellsCommand))]
        public static void UnprotectAllWellsCommand()
        {
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;
            string recordName = typeof(WellModel).Name;

            ed.WriteMessage($"\n开始解除保护，记录名: '{recordName}'");

            // 调用解锁方法
            int unprotectedCount = EntityProtectionManager.UnprotectEntitiesByXRecord(recordName);

            ed.WriteMessage($"\n命令执行完毕，自动解锁了 {unprotectedCount} 个工井模型。");
        }

        [CommandTag("排    模", nameof(CheckProtectionStatusCmd), "检查保护状态")]
        [CommandMethod(nameof(CheckProtectionStatusCmd))]
        public static void CheckProtectionStatusCmd()
        {
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;
            string recordName = typeof(WellModel).Name;

            ed.WriteMessage($"\n开始检查保护状态，记录名: '{recordName}'");
            EntityProtectionManager.GetProtectionStatistics(recordName);
        }

        [CommandTag("排    模", nameof(InitProtectionSystemCmd), "初始化保护系统")]
        [CommandMethod(nameof(InitProtectionSystemCmd))]
        public static void InitProtectionSystemCmd()
        {
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;
            ed.WriteMessage("\n正在初始化图元保护系统...");
            
            EntityProtectionManager.InitializeProtectionSystem();
            
            ed.WriteMessage("\n图元保护系统已初始化完成！");
            ed.WriteMessage("\n现在受保护的图元将无法被修改或删除（除非是保护用户本人）。");
        }

        [CommandTag("排    模", nameof(CleanupProtectionSystemCmd), "清理保护系统")]
        [CommandMethod(nameof(CleanupProtectionSystemCmd))]
        public static void CleanupProtectionSystemCmd()
        {
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;
            ed.WriteMessage("\n正在清理图元保护系统...");
            
            EntityProtectionManager.CleanupProtectionSystem();
            
            ed.WriteMessage("\n图元保护系统已清理完成！");
            ed.WriteMessage("\n注意：保护标记仍然存在，但不再阻止编辑操作。");
        }

        [CommandTag("排    模", nameof(TestProtectionCmd), "测试保护功能")]
        [CommandMethod(nameof(TestProtectionCmd))]
        public static void TestProtectionCmd()
        {
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;

            ed.WriteMessage("\n=== 图元完全锁定保护功能测试 ===");
            ed.WriteMessage("\n1. 保护系统已自动初始化（插件加载时）");
            ed.WriteMessage("\n2. 运行 LockEntitiesDebugCmd 完全锁定图元");
            ed.WriteMessage("\n3. 尝试选择并修改/删除受保护的图元（任何人都无法修改）");
            ed.WriteMessage("\n4. 观察保护警告和自动恢复功能");
            ed.WriteMessage("\n5. 运行 CheckProtectionStatusCmd 查看保护状态");
            ed.WriteMessage("\n6. 运行 UnprotectAllWellsCommand 解除保护");
            ed.WriteMessage("\n7. 再次尝试修改图元，应该可以正常编辑");

            // 显示当前用户信息
            ed.WriteMessage($"\n当前用户: {Environment.UserName}");
            ed.WriteMessage("\n注意：新版本实现完全锁定，任何人（包括保护用户）都无法修改！");
            ed.WriteMessage("\n受保护的图元会变为深灰色，便于识别。");
        }

        /// <summary>
        /// 超级保护命令 - 应用最强保护
        /// </summary>
        [CommandTag("排    模", nameof(SuperProtectCmd), "超级保护")]
        [CommandMethod(nameof(SuperProtectCmd))]
        public static void SuperProtectCmd()
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;

            ed.WriteMessage("\n=== 超级保护模式 ===");
            ed.WriteMessage("\n这将对所有WellModel图元应用最强保护措施！");

            // 确认操作
            var pko = new PromptKeywordOptions("\n确定要应用超级保护吗？[是(Y)/否(N)]");
            pko.Keywords.Add("Y");
            pko.Keywords.Add("N");
            pko.Keywords.Default = "N";

            var pkr = ed.GetKeywords(pko);
            if (pkr.Status != PromptStatus.OK || pkr.StringResult != "Y")
            {
                ed.WriteMessage("\n操作已取消。");
                return;
            }

            string recordName = typeof(WellModel).Name;

            // 先应用常规保护
            int protectedCount = EntityProtectionManager.ProtectEntitiesByXRecord(
                recordName,
                EntityProtectionManager.ProtectionLevel.Full,
                "SYSTEM_SUPER_LOCK",
                forceUpdate: true);

            ed.WriteMessage($"\n超级保护完成！已保护 {protectedCount} 个图元。");
            ed.WriteMessage("\n图元现在处于最高级别保护状态，任何编辑操作都将被阻止！");
            ed.WriteMessage("\n受保护的图元已变为深灰色标识。");
        }

        /// <summary>
        /// 测试保护效果命令
        /// </summary>
        [CommandTag("排    模", nameof(TestProtectionEffectCmd), "测试保护效果")]
        [CommandMethod(nameof(TestProtectionEffectCmd))]
        public static void TestProtectionEffectCmd()
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;
            var db = doc.Database;

            ed.WriteMessage("\n=== 保护效果测试 ===");
            ed.WriteMessage("\n请选择一个受保护的图元进行测试:");

            // 选择图元
            var peo = new PromptEntityOptions("\n选择要测试的图元: ");
            peo.AllowNone = false;

            var per = ed.GetEntity(peo);
            if (per.Status != PromptStatus.OK)
            {
                ed.WriteMessage("\n测试已取消。");
                return;
            }

            ObjectId entityId = per.ObjectId;

            // 检查是否受保护
            if (EntityProtectionManager.IsEntityProtected(entityId, out var level, out var user))
            {
                ed.WriteMessage($"\n图元 {entityId.Handle} 受保护状态:");
                ed.WriteMessage($"  保护级别: {level}");
                ed.WriteMessage($"  保护用户: {user}");
                ed.WriteMessage($"  当前用户: {Environment.UserName}");

                ed.WriteMessage("\n开始测试保护效果...");

                // 测试1：尝试修改图元
                ed.WriteMessage("\n测试1: 尝试修改图元属性...");
                try
                {
                    using (var tr = db.TransactionManager.StartTransaction())
                    {
                        var entity = tr.GetObject(entityId, OpenMode.ForWrite) as Entity;
                        if (entity != null)
                        {
                            // 尝试修改颜色
                            var originalColor = entity.ColorIndex;
                            entity.ColorIndex = 1; // 红色
                            tr.Commit();
                            ed.WriteMessage("  结果: 修改成功（这不应该发生！）");
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    ed.WriteMessage($"  结果: 修改被阻止 - {ex.Message}");
                }

                // 测试2：尝试删除图元
                ed.WriteMessage("\n测试2: 尝试删除图元...");
                try
                {
                    using (var tr = db.TransactionManager.StartTransaction())
                    {
                        var entity = tr.GetObject(entityId, OpenMode.ForWrite);
                        entity.Erase();
                        tr.Commit();
                        ed.WriteMessage("  结果: 删除成功（这不应该发生！）");
                    }
                }
                catch (System.Exception ex)
                {
                    ed.WriteMessage($"  结果: 删除被阻止 - {ex.Message}");
                }

                ed.WriteMessage("\n保护效果测试完成！");
            }
            else
            {
                ed.WriteMessage($"\n图元 {entityId.Handle} 未受保护。");
                ed.WriteMessage("请先运行保护命令，然后再进行测试。");
            }
        }

        /// <summary>
        /// 图层保护命令 - 最强保护方式
        /// </summary>
        [CommandTag("排    模", nameof(LayerProtectCmd), "图层保护")]
        [CommandMethod(nameof(LayerProtectCmd))]
        public static void LayerProtectCmd()
        {
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;
            string recordName = typeof(WellModel).Name;

            ed.WriteMessage($"\n开始图层保护，查找记录名: '{recordName}'");
            ed.WriteMessage("\n这是最强的保护方式，将图元移动到锁定的保护图层！");

            // 应用图层保护
            int protectedCount = LayerProtectionManager.ProtectEntitiesByLayer(recordName);

            ed.WriteMessage($"\n图层保护完成！已保护 {protectedCount} 个工井模型。");
            ed.WriteMessage("\n图元已移动到锁定的保护图层，完全无法编辑！");
            ed.WriteMessage("\n受保护的图元现在显示为深灰色。");
        }

        /// <summary>
        /// 图层解除保护命令
        /// </summary>
        [CommandTag("排    模", nameof(LayerUnprotectCmd), "图层解除保护")]
        [CommandMethod(nameof(LayerUnprotectCmd))]
        public static void LayerUnprotectCmd()
        {
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;
            string recordName = typeof(WellModel).Name;

            ed.WriteMessage($"\n开始解除图层保护，记录名: '{recordName}'");

            // 先解锁保护图层
            LayerProtectionManager.UnlockAllProtectedLayers();

            // 解除图层保护
            int unprotectedCount = LayerProtectionManager.UnprotectEntitiesByLayer(recordName);

            // 清理空的保护图层
            LayerProtectionManager.CleanupEmptyProtectedLayers();

            ed.WriteMessage($"\n图层保护解除完成！已解除 {unprotectedCount} 个工井模型的保护。");
            ed.WriteMessage("\n图元已恢复到原始图层，现在可以正常编辑。");
        }

        /// <summary>
        /// 锁定保护图层命令
        /// </summary>
        [CommandTag("排    模", nameof(LockProtectedLayersCmd), "锁定保护图层")]
        [CommandMethod(nameof(LockProtectedLayersCmd))]
        public static void LockProtectedLayersCmd()
        {
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;
            ed.WriteMessage("\n锁定所有保护图层...");

            LayerProtectionManager.LockAllProtectedLayers();

            ed.WriteMessage("\n所有保护图层已锁定！图元现在完全无法编辑。");
        }

        /// <summary>
        /// 解锁保护图层命令
        /// </summary>
        [CommandTag("排    模", nameof(UnlockProtectedLayersCmd), "解锁保护图层")]
        [CommandMethod(nameof(UnlockProtectedLayersCmd))]
        public static void UnlockProtectedLayersCmd()
        {
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;
            ed.WriteMessage("\n解锁所有保护图层...");

            LayerProtectionManager.UnlockAllProtectedLayers();

            ed.WriteMessage("\n所有保护图层已解锁！现在可以编辑图元（但仍建议先解除保护）。");
        }

        /// <summary>
        /// 安全选择命令 - 自动排除受保护的图元
        /// </summary>
        [CommandTag("排    模", nameof(SafeSelectCmd), "安全选择")]
        [CommandMethod(nameof(SafeSelectCmd))]
        public static void SafeSelectCmd()
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;

            ed.WriteMessage("\n安全选择模式 - 将自动排除受保护的图元");

            var result = CommandInterceptor.GetSelectionExcludingProtected(ed, "\n选择图元（受保护的图元将被自动排除）: ");

            if (result.Status == PromptStatus.OK && result.Value != null)
            {
                ed.WriteMessage($"\n成功选择了 {result.Value.Count} 个可编辑的图元。");
                ed.WriteMessage("\n现在您可以安全地对这些图元执行编辑操作。");

                // 设置为当前选择集
                ed.SetImpliedSelection(result.Value.GetObjectIds());
            }
            else if (result.Status == PromptStatus.Cancel)
            {
                ed.WriteMessage("\n选择已取消或所有图元都受保护。");
            }
        }

        /// <summary>
        /// 检查选择集保护状态
        /// </summary>
        [CommandTag("排    模", nameof(CheckSelectionProtectionCmd), "检查选择保护")]
        [CommandMethod(nameof(CheckSelectionProtectionCmd))]
        public static void CheckSelectionProtectionCmd()
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;

            ed.WriteMessage("\n检查当前选择集的保护状态...");

            var selection = ed.SelectImplied();
            if (selection.Status != PromptStatus.OK || selection.Value == null)
            {
                ed.WriteMessage("\n没有选择任何图元。");
                return;
            }

            int totalCount = selection.Value.Count;
            int protectedCount = 0;
            var protectedEntities = new List<string>();

            foreach (ObjectId objId in selection.Value.GetObjectIds())
            {
                if (EntityProtectionManager.IsEntityProtected(objId, out var level, out var user))
                {
                    protectedCount++;
                    protectedEntities.Add($"{objId.Handle}({user},{level})");
                }
            }

            ed.WriteMessage($"\n选择集统计:");
            ed.WriteMessage($"  总图元数: {totalCount}");
            ed.WriteMessage($"  受保护: {protectedCount}");
            ed.WriteMessage($"  可编辑: {totalCount - protectedCount}");

            if (protectedCount > 0)
            {
                ed.WriteMessage($"\n受保护的图元详情:");
                foreach (string info in protectedEntities)
                {
                    ed.WriteMessage($"  {info}");
                }
                ed.WriteMessage("\n⚠️ 警告：对这些图元的编辑操作可能会被监控或阻止！");
            }
            else
            {
                ed.WriteMessage("\n✅ 所有选择的图元都可以安全编辑。");
            }
        }

        /// <summary>
        /// 启用/禁用命令拦截器
        /// </summary>
        [CommandTag("排    模", nameof(ToggleCommandInterceptorCmd), "切换命令拦截")]
        [CommandMethod(nameof(ToggleCommandInterceptorCmd))]
        public static void ToggleCommandInterceptorCmd()
        {
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;

            // 这里需要添加一个状态检查方法到CommandInterceptor
            ed.WriteMessage("\n切换命令拦截器状态...");

            // 简单的重新初始化
            EntityProtectionManager.CleanupProtectionSystem();
            System.Threading.Thread.Sleep(100);
            EntityProtectionManager.InitializeProtectionSystem();

            ed.WriteMessage("\n命令拦截器已重新初始化。");
        }

        /// <summary>
        /// 选择性保护图元（手动选择）
        /// </summary>
        [CommandTag("排    模", nameof(ProtectSelectedEntitiesCmd), "选择保护图元")]
        [CommandMethod(nameof(ProtectSelectedEntitiesCmd))]
        public static void ProtectSelectedEntitiesCmd()
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;
            var db = doc.Database;

            // 确保保护系统已初始化
            EntityProtectionManager.InitializeProtectionSystem();

            // 选择图元
            var pso = new PromptSelectionOptions();
            pso.MessageForAdding = "\n请选择要保护的图元: ";
            pso.AllowDuplicates = false;

            var psr = ed.GetSelection(pso);
            if (psr.Status != PromptStatus.OK)
            {
                ed.WriteMessage("\n操作已取消。");
                return;
            }

            int successCount = 0;
            int failedCount = 0;

            using (var docLock = doc.LockDocument())
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    foreach (ObjectId objId in psr.Value.GetObjectIds())
                    {
                        try
                        {
                            var entity = tr.GetObject(objId, OpenMode.ForWrite);
                            if (entity == null || entity.IsErased) continue;

                            // 创建或获取扩展字典
                            if (entity.ExtensionDictionary.IsNull)
                            {
                                entity.CreateExtensionDictionary();
                            }

                            var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForWrite) as DBDictionary;
                            if (extDict == null) continue;

                            // 检查是否已保护
                            if (extDict.Contains("ENTITY_PROTECTION"))
                            {
                                ed.WriteMessage($"\n图元 {objId.Handle} 已经受保护，跳过。");
                                continue;
                            }

                            // 添加保护
                            var protectionDict = new DBDictionary();
                            extDict.SetAt("ENTITY_PROTECTION", protectionDict);
                            tr.AddNewlyCreatedDBObject(protectionDict, true);

                            // 添加保护数据
                            var levelRec = new Xrecord { Data = new ResultBuffer(new TypedValue((int)DxfCode.Text, "Full")) };
                            protectionDict.SetAt("PROTECTION_LEVEL", levelRec);
                            tr.AddNewlyCreatedDBObject(levelRec, true);

                            var timeRec = new Xrecord { Data = new ResultBuffer(new TypedValue((int)DxfCode.Text, DateTime.Now.ToString("o"))) };
                            protectionDict.SetAt("PROTECTION_TIME", timeRec);
                            tr.AddNewlyCreatedDBObject(timeRec, true);

                            var userRec = new Xrecord { Data = new ResultBuffer(new TypedValue((int)DxfCode.Text, Environment.UserName)) };
                            protectionDict.SetAt("PROTECTION_USER", userRec);
                            tr.AddNewlyCreatedDBObject(userRec, true);

                            ed.WriteMessage($"\n已保护图元: {objId.Handle} 类型: {entity.GetType().Name}");
                            successCount++;
                        }
                        catch (System.Exception ex)
                        {
                            ed.WriteMessage($"\n保护图元 {objId.Handle} 失败: {ex.Message}");
                            failedCount++;
                        }
                    }

                    tr.Commit();
                    ed.WriteMessage($"\n保护完成: 成功 {successCount} 个，失败 {failedCount} 个");
                }
                catch (System.Exception ex)
                {
                    ed.WriteMessage($"\n保护操作失败: {ex.Message}");
                    tr.Abort();
                }
            }
        }

        /// <summary>
        /// 选择性解除保护（手动选择）
        /// </summary>
        [CommandTag("排    模", nameof(UnprotectSelectedEntitiesCmd), "选择解除保护")]
        [CommandMethod(nameof(UnprotectSelectedEntitiesCmd))]
        public static void UnprotectSelectedEntitiesCmd()
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;
            var db = doc.Database;

            // 选择图元
            var pso = new PromptSelectionOptions();
            pso.MessageForAdding = "\n请选择要解除保护的图元: ";
            pso.AllowDuplicates = false;

            var psr = ed.GetSelection(pso);
            if (psr.Status != PromptStatus.OK)
            {
                ed.WriteMessage("\n操作已取消。");
                return;
            }

            int successCount = 0;
            int failedCount = 0;

            using (var docLock = doc.LockDocument())
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    foreach (ObjectId objId in psr.Value.GetObjectIds())
                    {
                        try
                        {
                            var entity = tr.GetObject(objId, OpenMode.ForWrite);
                            if (entity == null || entity.IsErased) continue;

                            if (entity.ExtensionDictionary.IsNull)
                            {
                                ed.WriteMessage($"\n图元 {objId.Handle} 没有扩展字典，跳过。");
                                continue;
                            }

                            var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForWrite) as DBDictionary;
                            if (extDict == null || !extDict.Contains("ENTITY_PROTECTION"))
                            {
                                ed.WriteMessage($"\n图元 {objId.Handle} 未受保护，跳过。");
                                continue;
                            }

                            // 删除保护字典
                            var protectionDict = tr.GetObject(extDict.GetAt("ENTITY_PROTECTION"), OpenMode.ForWrite) as DBDictionary;
                            if (protectionDict != null)
                            {
                                protectionDict.Erase();
                                extDict.Remove("ENTITY_PROTECTION");
                                ed.WriteMessage($"\n已解除保护: {objId.Handle} 类型: {entity.GetType().Name}");
                                successCount++;
                            }
                        }
                        catch (System.Exception ex)
                        {
                            ed.WriteMessage($"\n解除保护图元 {objId.Handle} 失败: {ex.Message}");
                            failedCount++;
                        }
                    }

                    tr.Commit();
                    ed.WriteMessage($"\n解除保护完成: 成功 {successCount} 个，失败 {failedCount} 个");
                }
                catch (System.Exception ex)
                {
                    ed.WriteMessage($"\n解除保护操作失败: {ex.Message}");
                    tr.Abort();
                }
            }
        }
    }
    #endregion
}
