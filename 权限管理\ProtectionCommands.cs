using SmartRouting.App.Commands.数据录入.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;

namespace SmartRouting.App.Commands.权限管理
{
    #region 用户命令
    public class ProtectionCommands
    {
        [CommandTag("排    模", nameof(LockEntitiesDebugCmd), "文档保护")]
        [CommandMethod(nameof(LockEntitiesDebugCmd))]
        public static void LockEntitiesDebugCmd()
        {
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;
            string recordName = typeof(WellModel).Name;

            ed.WriteMessage($"\n开始调试，查找记录名: '{recordName}'");

            // 使用强制更新参数，确保保护系统已初始化
            int protectedCount = EntityProtectionManager.ProtectEntitiesByXRecord(
                recordName, 
                EntityProtectionManager.ProtectionLevel.Full, 
                null, 
                forceUpdate: true);

            ed.WriteMessage($"\n命令执行完毕，自动保护了 {protectedCount} 个工井模型。");
            ed.WriteMessage($"\n提示：图元现在已受到真正的编辑保护，尝试修改或删除将被阻止！");
        }

        [CommandTag("排    模", nameof(UnprotectAllWellsCommand), "文档保护解锁")]
        [CommandMethod(nameof(UnprotectAllWellsCommand))]
        public static void UnprotectAllWellsCommand()
        {
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;
            string recordName = typeof(WellModel).Name;

            ed.WriteMessage($"\n开始解除保护，记录名: '{recordName}'");

            // 调用解锁方法
            int unprotectedCount = EntityProtectionManager.UnprotectEntitiesByXRecord(recordName);

            ed.WriteMessage($"\n命令执行完毕，自动解锁了 {unprotectedCount} 个工井模型。");
        }

        [CommandTag("排    模", nameof(CheckProtectionStatusCmd), "检查保护状态")]
        [CommandMethod(nameof(CheckProtectionStatusCmd))]
        public static void CheckProtectionStatusCmd()
        {
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;
            string recordName = typeof(WellModel).Name;

            ed.WriteMessage($"\n开始检查保护状态，记录名: '{recordName}'");
            EntityProtectionManager.GetProtectionStatistics(recordName);
        }

        [CommandTag("排    模", nameof(InitProtectionSystemCmd), "初始化保护系统")]
        [CommandMethod(nameof(InitProtectionSystemCmd))]
        public static void InitProtectionSystemCmd()
        {
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;
            ed.WriteMessage("\n正在初始化图元保护系统...");
            
            EntityProtectionManager.InitializeProtectionSystem();
            
            ed.WriteMessage("\n图元保护系统已初始化完成！");
            ed.WriteMessage("\n现在受保护的图元将无法被修改或删除（除非是保护用户本人）。");
        }

        [CommandTag("排    模", nameof(CleanupProtectionSystemCmd), "清理保护系统")]
        [CommandMethod(nameof(CleanupProtectionSystemCmd))]
        public static void CleanupProtectionSystemCmd()
        {
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;
            ed.WriteMessage("\n正在清理图元保护系统...");
            
            EntityProtectionManager.CleanupProtectionSystem();
            
            ed.WriteMessage("\n图元保护系统已清理完成！");
            ed.WriteMessage("\n注意：保护标记仍然存在，但不再阻止编辑操作。");
        }

        [CommandTag("排    模", nameof(TestProtectionCmd), "测试保护功能")]
        [CommandMethod(nameof(TestProtectionCmd))]
        public static void TestProtectionCmd()
        {
            var ed = Application.DocumentManager.MdiActiveDocument.Editor;
            
            ed.WriteMessage("\n=== 图元保护功能测试 ===");
            ed.WriteMessage("\n1. 请先运行 InitProtectionSystemCmd 初始化保护系统");
            ed.WriteMessage("\n2. 然后运行 LockEntitiesDebugCmd 保护图元");
            ed.WriteMessage("\n3. 尝试选择并修改/删除受保护的图元");
            ed.WriteMessage("\n4. 观察是否出现保护警告并阻止操作");
            ed.WriteMessage("\n5. 运行 CheckProtectionStatusCmd 查看保护状态");
            ed.WriteMessage("\n6. 运行 UnprotectAllWellsCommand 解除保护");
            ed.WriteMessage("\n7. 再次尝试修改图元，应该可以正常编辑");
            
            // 显示当前用户信息
            ed.WriteMessage($"\n当前用户: {Environment.UserName}");
            ed.WriteMessage("\n注意：只有保护用户本人可以修改自己保护的图元！");
        }

        /// <summary>
        /// 选择性保护图元（手动选择）
        /// </summary>
        [CommandTag("排    模", nameof(ProtectSelectedEntitiesCmd), "选择保护图元")]
        [CommandMethod(nameof(ProtectSelectedEntitiesCmd))]
        public static void ProtectSelectedEntitiesCmd()
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;
            var db = doc.Database;

            // 确保保护系统已初始化
            EntityProtectionManager.InitializeProtectionSystem();

            // 选择图元
            var pso = new PromptSelectionOptions();
            pso.MessageForAdding = "\n请选择要保护的图元: ";
            pso.AllowDuplicates = false;

            var psr = ed.GetSelection(pso);
            if (psr.Status != PromptStatus.OK)
            {
                ed.WriteMessage("\n操作已取消。");
                return;
            }

            int successCount = 0;
            int failedCount = 0;

            using (var docLock = doc.LockDocument())
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    foreach (ObjectId objId in psr.Value.GetObjectIds())
                    {
                        try
                        {
                            var entity = tr.GetObject(objId, OpenMode.ForWrite);
                            if (entity == null || entity.IsErased) continue;

                            // 创建或获取扩展字典
                            if (entity.ExtensionDictionary.IsNull)
                            {
                                entity.CreateExtensionDictionary();
                            }

                            var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForWrite) as DBDictionary;
                            if (extDict == null) continue;

                            // 检查是否已保护
                            if (extDict.Contains("ENTITY_PROTECTION"))
                            {
                                ed.WriteMessage($"\n图元 {objId.Handle} 已经受保护，跳过。");
                                continue;
                            }

                            // 添加保护
                            var protectionDict = new DBDictionary();
                            extDict.SetAt("ENTITY_PROTECTION", protectionDict);
                            tr.AddNewlyCreatedDBObject(protectionDict, true);

                            // 添加保护数据
                            var levelRec = new Xrecord { Data = new ResultBuffer(new TypedValue((int)DxfCode.Text, "Full")) };
                            protectionDict.SetAt("PROTECTION_LEVEL", levelRec);
                            tr.AddNewlyCreatedDBObject(levelRec, true);

                            var timeRec = new Xrecord { Data = new ResultBuffer(new TypedValue((int)DxfCode.Text, DateTime.Now.ToString("o"))) };
                            protectionDict.SetAt("PROTECTION_TIME", timeRec);
                            tr.AddNewlyCreatedDBObject(timeRec, true);

                            var userRec = new Xrecord { Data = new ResultBuffer(new TypedValue((int)DxfCode.Text, Environment.UserName)) };
                            protectionDict.SetAt("PROTECTION_USER", userRec);
                            tr.AddNewlyCreatedDBObject(userRec, true);

                            ed.WriteMessage($"\n已保护图元: {objId.Handle} 类型: {entity.GetType().Name}");
                            successCount++;
                        }
                        catch (System.Exception ex)
                        {
                            ed.WriteMessage($"\n保护图元 {objId.Handle} 失败: {ex.Message}");
                            failedCount++;
                        }
                    }

                    tr.Commit();
                    ed.WriteMessage($"\n保护完成: 成功 {successCount} 个，失败 {failedCount} 个");
                }
                catch (System.Exception ex)
                {
                    ed.WriteMessage($"\n保护操作失败: {ex.Message}");
                    tr.Abort();
                }
            }
        }

        /// <summary>
        /// 选择性解除保护（手动选择）
        /// </summary>
        [CommandTag("排    模", nameof(UnprotectSelectedEntitiesCmd), "选择解除保护")]
        [CommandMethod(nameof(UnprotectSelectedEntitiesCmd))]
        public static void UnprotectSelectedEntitiesCmd()
        {
            var doc = Application.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;
            var db = doc.Database;

            // 选择图元
            var pso = new PromptSelectionOptions();
            pso.MessageForAdding = "\n请选择要解除保护的图元: ";
            pso.AllowDuplicates = false;

            var psr = ed.GetSelection(pso);
            if (psr.Status != PromptStatus.OK)
            {
                ed.WriteMessage("\n操作已取消。");
                return;
            }

            int successCount = 0;
            int failedCount = 0;

            using (var docLock = doc.LockDocument())
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    foreach (ObjectId objId in psr.Value.GetObjectIds())
                    {
                        try
                        {
                            var entity = tr.GetObject(objId, OpenMode.ForWrite);
                            if (entity == null || entity.IsErased) continue;

                            if (entity.ExtensionDictionary.IsNull)
                            {
                                ed.WriteMessage($"\n图元 {objId.Handle} 没有扩展字典，跳过。");
                                continue;
                            }

                            var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForWrite) as DBDictionary;
                            if (extDict == null || !extDict.Contains("ENTITY_PROTECTION"))
                            {
                                ed.WriteMessage($"\n图元 {objId.Handle} 未受保护，跳过。");
                                continue;
                            }

                            // 删除保护字典
                            var protectionDict = tr.GetObject(extDict.GetAt("ENTITY_PROTECTION"), OpenMode.ForWrite) as DBDictionary;
                            if (protectionDict != null)
                            {
                                protectionDict.Erase();
                                extDict.Remove("ENTITY_PROTECTION");
                                ed.WriteMessage($"\n已解除保护: {objId.Handle} 类型: {entity.GetType().Name}");
                                successCount++;
                            }
                        }
                        catch (System.Exception ex)
                        {
                            ed.WriteMessage($"\n解除保护图元 {objId.Handle} 失败: {ex.Message}");
                            failedCount++;
                        }
                    }

                    tr.Commit();
                    ed.WriteMessage($"\n解除保护完成: 成功 {successCount} 个，失败 {failedCount} 个");
                }
                catch (System.Exception ex)
                {
                    ed.WriteMessage($"\n解除保护操作失败: {ex.Message}");
                    tr.Abort();
                }
            }
        }
    }
    #endregion
}
