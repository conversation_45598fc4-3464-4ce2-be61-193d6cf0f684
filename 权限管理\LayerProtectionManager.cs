using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;

namespace SmartRouting.App.Commands.权限管理
{
    /// <summary>
    /// 基于图层的保护管理器 - 提供更强力的保护机制
    /// </summary>
    public static class LayerProtectionManager
    {
        private const string PROTECTED_LAYER_PREFIX = "PROTECTED_";
        private static readonly Dictionary<string, string> _originalLayers = new Dictionary<string, string>();

        /// <summary>
        /// 通过移动到保护图层来保护图元
        /// </summary>
        public static int ProtectEntitiesByLayer(string recordName)
        {
            var db = HostApplicationServices.WorkingDatabase;
            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null) return 0;

            int successCount = 0;
            string protectedLayerName = PROTECTED_LAYER_PREFIX + DateTime.Now.ToString("yyyyMMdd_HHmmss");

            using (var docLock = doc.LockDocument())
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    // 创建保护图层
                    var layerTable = (LayerTable)tr.GetObject(db.LayerTableId, OpenMode.ForWrite);
                    LayerTableRecord protectedLayer = null;

                    if (!layerTable.Has(protectedLayerName))
                    {
                        protectedLayer = new LayerTableRecord();
                        protectedLayer.Name = protectedLayerName;
                        protectedLayer.Color = Autodesk.AutoCAD.Colors.Color.FromColorIndex(Autodesk.AutoCAD.Colors.ColorMethod.ByAci, 8); // 深灰色
                        protectedLayer.IsLocked = true; // 锁定图层
                        protectedLayer.IsFrozen = false; // 不冻结，保持可见
                        protectedLayer.IsOff = false;

                        layerTable.Add(protectedLayer);
                        tr.AddNewlyCreatedDBObject(protectedLayer, true);
                        LogMessage($"创建保护图层: {protectedLayerName}");
                    }
                    else
                    {
                        protectedLayer = (LayerTableRecord)tr.GetObject(layerTable[protectedLayerName], OpenMode.ForWrite);
                        protectedLayer.IsLocked = true;
                    }

                    // 查找并移动图元到保护图层
                    var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                    var ms = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                    foreach (ObjectId objId in ms)
                    {
                        try
                        {
                            if (objId.IsNull || objId.IsErased || !objId.IsValid) continue;

                            var entity = tr.GetObject(objId, OpenMode.ForWrite, false) as BlockReference;
                            if (entity == null || entity.IsErased) continue;

                            // 检查是否有指定的业务记录
                            if (entity.ExtensionDictionary.IsNull) continue;
                            var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForRead, false) as DBDictionary;
                            if (extDict == null || !extDict.Contains(recordName)) continue;

                            // 保存原始图层信息
                            string originalLayer = entity.Layer;
                            if (!_originalLayers.ContainsKey(objId.Handle.ToString()))
                            {
                                _originalLayers[objId.Handle.ToString()] = originalLayer;
                            }

                            // 移动到保护图层
                            entity.Layer = protectedLayerName;
                            
                            // 添加保护标记
                            EntityProtectionManager.ApplyAdvancedProtection(objId);

                            LogMessage($"图元 {objId.Handle} 已移动到保护图层: {protectedLayerName}");
                            successCount++;
                        }
                        catch (System.Exception ex)
                        {
                            LogMessage($"保护图元 {objId.Handle} 失败: {ex.Message}");
                        }
                    }

                    tr.Commit();
                    LogMessage($"图层保护完成: 共保护了 {successCount} 个图元到图层 {protectedLayerName}");
                }
                catch (System.Exception ex)
                {
                    LogMessage($"图层保护操作失败: {ex.Message}");
                    tr.Abort();
                }
            }

            return successCount;
        }

        /// <summary>
        /// 解除图层保护
        /// </summary>
        public static int UnprotectEntitiesByLayer(string recordName)
        {
            var db = HostApplicationServices.WorkingDatabase;
            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null) return 0;

            int successCount = 0;

            using (var docLock = doc.LockDocument())
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                    var ms = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                    foreach (ObjectId objId in ms)
                    {
                        try
                        {
                            if (objId.IsNull || objId.IsErased || !objId.IsValid) continue;

                            var entity = tr.GetObject(objId, OpenMode.ForWrite, false) as BlockReference;
                            if (entity == null || entity.IsErased) continue;

                            // 检查是否在保护图层
                            if (!entity.Layer.StartsWith(PROTECTED_LAYER_PREFIX)) continue;

                            // 检查是否有指定的业务记录
                            if (entity.ExtensionDictionary.IsNull) continue;
                            var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForRead, false) as DBDictionary;
                            if (extDict == null || !extDict.Contains(recordName)) continue;

                            // 恢复原始图层
                            string handleStr = objId.Handle.ToString();
                            if (_originalLayers.ContainsKey(handleStr))
                            {
                                entity.Layer = _originalLayers[handleStr];
                                _originalLayers.Remove(handleStr);
                            }
                            else
                            {
                                entity.Layer = "0"; // 默认图层
                            }

                            // 移除保护标记
                            EntityProtectionManager.RemoveAdvancedProtection(objId);

                            LogMessage($"图元 {objId.Handle} 已恢复到原始图层");
                            successCount++;
                        }
                        catch (System.Exception ex)
                        {
                            LogMessage($"解除保护图元 {objId.Handle} 失败: {ex.Message}");
                        }
                    }

                    tr.Commit();
                    LogMessage($"图层保护解除完成: 共解除了 {successCount} 个图元的保护");
                }
                catch (System.Exception ex)
                {
                    LogMessage($"解除图层保护操作失败: {ex.Message}");
                    tr.Abort();
                }
            }

            return successCount;
        }

        /// <summary>
        /// 锁定所有保护图层
        /// </summary>
        public static void LockAllProtectedLayers()
        {
            var db = HostApplicationServices.WorkingDatabase;
            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null) return;

            using (var docLock = doc.LockDocument())
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var layerTable = (LayerTable)tr.GetObject(db.LayerTableId, OpenMode.ForRead);
                    
                    foreach (ObjectId layerId in layerTable)
                    {
                        var layer = (LayerTableRecord)tr.GetObject(layerId, OpenMode.ForWrite);
                        if (layer.Name.StartsWith(PROTECTED_LAYER_PREFIX))
                        {
                            layer.IsLocked = true;
                            LogMessage($"锁定保护图层: {layer.Name}");
                        }
                    }

                    tr.Commit();
                    LogMessage("所有保护图层已锁定");
                }
                catch (System.Exception ex)
                {
                    LogMessage($"锁定保护图层失败: {ex.Message}");
                    tr.Abort();
                }
            }
        }

        /// <summary>
        /// 解锁所有保护图层
        /// </summary>
        public static void UnlockAllProtectedLayers()
        {
            var db = HostApplicationServices.WorkingDatabase;
            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null) return;

            using (var docLock = doc.LockDocument())
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var layerTable = (LayerTable)tr.GetObject(db.LayerTableId, OpenMode.ForRead);
                    
                    foreach (ObjectId layerId in layerTable)
                    {
                        var layer = (LayerTableRecord)tr.GetObject(layerId, OpenMode.ForWrite);
                        if (layer.Name.StartsWith(PROTECTED_LAYER_PREFIX))
                        {
                            layer.IsLocked = false;
                            LogMessage($"解锁保护图层: {layer.Name}");
                        }
                    }

                    tr.Commit();
                    LogMessage("所有保护图层已解锁");
                }
                catch (System.Exception ex)
                {
                    LogMessage($"解锁保护图层失败: {ex.Message}");
                    tr.Abort();
                }
            }
        }

        /// <summary>
        /// 清理空的保护图层
        /// </summary>
        public static void CleanupEmptyProtectedLayers()
        {
            var db = HostApplicationServices.WorkingDatabase;
            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null) return;

            using (var docLock = doc.LockDocument())
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var layerTable = (LayerTable)tr.GetObject(db.LayerTableId, OpenMode.ForWrite);
                    var layersToDelete = new List<string>();
                    
                    foreach (ObjectId layerId in layerTable)
                    {
                        var layer = (LayerTableRecord)tr.GetObject(layerId, OpenMode.ForRead);
                        if (layer.Name.StartsWith(PROTECTED_LAYER_PREFIX))
                        {
                            // 检查图层是否为空
                            bool hasEntities = false;
                            var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                            var ms = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                            foreach (ObjectId objId in ms)
                            {
                                var entity = tr.GetObject(objId, OpenMode.ForRead) as Entity;
                                if (entity?.Layer == layer.Name)
                                {
                                    hasEntities = true;
                                    break;
                                }
                            }

                            if (!hasEntities)
                            {
                                layersToDelete.Add(layer.Name);
                            }
                        }
                    }

                    // 删除空图层
                    foreach (string layerName in layersToDelete)
                    {
                        if (layerTable.Has(layerName))
                        {
                            var layer = (LayerTableRecord)tr.GetObject(layerTable[layerName], OpenMode.ForWrite);
                            layer.Erase();
                            LogMessage($"删除空的保护图层: {layerName}");
                        }
                    }

                    tr.Commit();
                    LogMessage($"清理完成: 删除了 {layersToDelete.Count} 个空的保护图层");
                }
                catch (System.Exception ex)
                {
                    LogMessage($"清理保护图层失败: {ex.Message}");
                    tr.Abort();
                }
            }
        }

        /// <summary>
        /// 日志输出
        /// </summary>
        private static void LogMessage(string message)
        {
            try
            {
                Application.DocumentManager.MdiActiveDocument?.Editor?.WriteMessage($"\n[图层保护] {message}");
            }
            catch { }
        }
    }
}
