using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;

namespace SmartRouting.App.Commands.权限管理
{
    /// <summary>
    /// 命令拦截器 - 更安全的保护方式
    /// </summary>
    public static class CommandInterceptor
    {
        private static bool _interceptorActive = false;
        private static readonly HashSet<string> _dangerousCommands = new HashSet<string>
        {
            "MOVE", "COPY", "ROTATE", "SCALE", "STRETCH", "TRIM", "EXTEND",
            "ERASE", "DELETE", "EXPLODE", "BREAK", "FILLET", "CHAMFER",
            "MIRROR", "ARRAY", "OFFSET", "EDIT", "PEDIT", "SPLINEDIT",
            "HATCHEDIT", "TEXTEDIT", "DDEDIT", "PROPERTIES", "MATCHPROP",
            "GRIP_STRETCH", "GRIP_MOVE", "GRIP_ROTATE", "GRIP_SCALE"
        };

        /// <summary>
        /// 启用命令拦截
        /// </summary>
        public static void EnableInterceptor()
        {
            if (_interceptorActive) return;

            try
            {
                Application.DocumentManager.DocumentActivated += DocumentManager_DocumentActivated;
                
                var doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.CommandWillStart += Document_CommandWillStart;
                    doc.CommandEnded += Document_CommandEnded;
                }

                _interceptorActive = true;
                LogMessage("命令拦截器已启用");
            }
            catch (System.Exception ex)
            {
                LogMessage($"启用命令拦截器失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 禁用命令拦截
        /// </summary>
        public static void DisableInterceptor()
        {
            if (!_interceptorActive) return;

            try
            {
                Application.DocumentManager.DocumentActivated -= DocumentManager_DocumentActivated;
                
                var doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.CommandWillStart -= Document_CommandWillStart;
                    doc.CommandEnded -= Document_CommandEnded;
                }

                _interceptorActive = false;
                LogMessage("命令拦截器已禁用");
            }
            catch (System.Exception ex)
            {
                LogMessage($"禁用命令拦截器失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 文档激活事件
        /// </summary>
        private static void DocumentManager_DocumentActivated(object sender, DocumentCollectionEventArgs e)
        {
            try
            {
                if (e.Document != null)
                {
                    e.Document.CommandWillStart += Document_CommandWillStart;
                    e.Document.CommandEnded += Document_CommandEnded;
                }
            }
            catch (System.Exception ex)
            {
                LogMessage($"文档激活事件处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 命令开始事件 - 检查是否涉及受保护的图元
        /// </summary>
        private static void Document_CommandWillStart(object sender, CommandEventArgs e)
        {
            try
            {
                string cmdName = e.GlobalCommandName.ToUpper();
                
                if (_dangerousCommands.Contains(cmdName))
                {
                    LogMessage($"检测到潜在危险命令: {cmdName}");
                    
                    // 检查当前选择集是否包含受保护的图元
                    var doc = Application.DocumentManager.MdiActiveDocument;
                    if (doc?.Editor != null)
                    {
                        var selection = doc.Editor.SelectImplied();
                        if (selection.Status == PromptStatus.OK && selection.Value != null)
                        {
                            bool hasProtectedEntities = false;
                            var protectedEntities = new List<string>();

                            foreach (ObjectId objId in selection.Value.GetObjectIds())
                            {
                                if (EntityProtectionManager.IsEntityProtected(objId, out var level, out var user))
                                {
                                    hasProtectedEntities = true;
                                    protectedEntities.Add($"{objId.Handle}({user})");
                                }
                            }

                            if (hasProtectedEntities)
                            {
                                string entityList = string.Join(", ", protectedEntities);
                                LogMessage($"警告：选择集包含受保护的图元: {entityList}");
                                
                                // 显示警告但不阻止命令（避免崩溃）
                                doc.Editor.WriteMessage($"\n⚠️ 警告：您选择的图元中包含受保护的对象！");
                                doc.Editor.WriteMessage($"\n受保护的图元: {entityList}");
                                doc.Editor.WriteMessage($"\n命令: {cmdName} 可能会修改这些受保护的图元！");
                                
                                // 可以选择性地取消选择受保护的图元
                                // 但这可能会影响用户体验
                            }
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                LogMessage($"命令开始事件处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 命令结束事件
        /// </summary>
        private static void Document_CommandEnded(object sender, CommandEventArgs e)
        {
            try
            {
                string cmdName = e.GlobalCommandName.ToUpper();
                
                if (_dangerousCommands.Contains(cmdName))
                {
                    LogMessage($"危险命令执行完成: {cmdName}");
                    
                    // 可以在这里检查是否有受保护的图元被意外修改
                    // 并尝试恢复或记录
                }
            }
            catch (System.Exception ex)
            {
                LogMessage($"命令结束事件处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查选择集中的受保护图元
        /// </summary>
        public static bool CheckSelectionForProtectedEntities(SelectionSet selection, out List<ObjectId> protectedIds)
        {
            protectedIds = new List<ObjectId>();
            
            if (selection == null) return false;

            foreach (ObjectId objId in selection.GetObjectIds())
            {
                if (EntityProtectionManager.IsEntityProtected(objId, out _, out _))
                {
                    protectedIds.Add(objId);
                }
            }

            return protectedIds.Count > 0;
        }

        /// <summary>
        /// 从选择集中移除受保护的图元
        /// </summary>
        public static SelectionSet RemoveProtectedEntitiesFromSelection(SelectionSet originalSelection)
        {
            if (originalSelection == null) return null;

            var allowedIds = new List<ObjectId>();
            var removedCount = 0;

            foreach (ObjectId objId in originalSelection.GetObjectIds())
            {
                if (!EntityProtectionManager.IsEntityProtected(objId, out _, out _))
                {
                    allowedIds.Add(objId);
                }
                else
                {
                    removedCount++;
                }
            }

            if (removedCount > 0)
            {
                LogMessage($"从选择集中移除了 {removedCount} 个受保护的图元");
            }

            return allowedIds.Count > 0 ? SelectionSet.FromObjectIds(allowedIds.ToArray()) : null;
        }

        /// <summary>
        /// 安全的选择过滤器 - 自动排除受保护的图元
        /// </summary>
        public static PromptSelectionResult GetSelectionExcludingProtected(Editor editor, string message = "\n选择图元: ")
        {
            var pso = new PromptSelectionOptions();
            pso.MessageForAdding = message;
            pso.AllowDuplicates = false;

            var result = editor.GetSelection(pso);
            
            if (result.Status == PromptStatus.OK && result.Value != null)
            {
                var filteredSelection = RemoveProtectedEntitiesFromSelection(result.Value);
                if (filteredSelection == null)
                {
                    editor.WriteMessage("\n所有选择的图元都受保护，操作已取消。");
                    return new PromptSelectionResult(PromptStatus.Cancel, null);
                }
                else if (filteredSelection.Count < result.Value.Count)
                {
                    editor.WriteMessage($"\n已自动排除 {result.Value.Count - filteredSelection.Count} 个受保护的图元。");
                    return new PromptSelectionResult(PromptStatus.OK, filteredSelection);
                }
            }

            return result;
        }

        /// <summary>
        /// 日志输出
        /// </summary>
        private static void LogMessage(string message)
        {
            try
            {
                Application.DocumentManager.MdiActiveDocument?.Editor?.WriteMessage($"\n[命令拦截] {message}");
            }
            catch { }
        }
    }
}
