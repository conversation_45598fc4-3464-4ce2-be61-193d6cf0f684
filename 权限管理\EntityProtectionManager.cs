using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;

namespace SmartRouting.App.Commands.权限管理
{
    public static class EntityProtectionManager
    {
        private const string PROTECTION_DICT_NAME = "ENTITY_PROTECTION";
        private const string PROTECTION_LEVEL_KEY = "PROTECTION_LEVEL";
        private const string PROTECTION_TIME_KEY = "PROTECTION_TIME";
        private const string PROTECTION_USER_KEY = "PROTECTION_USER";

        public enum ProtectionLevel { Full, ReadOnly, Partial, DeleteOnly }

        private static bool _eventsRegistered = false;

        /// <summary>
        /// 初始化保护系统（注册事件处理器）
        /// </summary>
        public static void InitializeProtectionSystem()
        {
            if (_eventsRegistered) return;

            try
            {
                // 注册数据库事件
                var db = HostApplicationServices.WorkingDatabase;
                if (db != null)
                {
                    db.ObjectModified += Database_ObjectModified;
                    db.ObjectErased += Database_ObjectErased;
                    db.ObjectOpenedForModify += Database_ObjectOpenedForModify;
                    LogError("保护系统事件处理器已注册");
                }

                // 注册文档事件
                Application.DocumentManager.DocumentActivated += DocumentManager_DocumentActivated;

                // 注册编辑器事件
                var doc = Application.DocumentManager.MdiActiveDocument;
                if (doc?.Editor != null)
                {
                    doc.CommandWillStart += Document_CommandWillStart;
                    doc.CommandEnded += Document_CommandEnded;
                }

                _eventsRegistered = true;
            }
            catch (System.Exception ex)
            {
                LogError($"初始化保护系统失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理保护系统（注销事件处理器）
        /// </summary>
        public static void CleanupProtectionSystem()
        {
            if (!_eventsRegistered) return;

            try
            {
                var db = HostApplicationServices.WorkingDatabase;
                if (db != null)
                {
                    db.ObjectModified -= Database_ObjectModified;
                    db.ObjectErased -= Database_ObjectErased;
                    db.ObjectOpenedForModify -= Database_ObjectOpenedForModify;
                }

                Application.DocumentManager.DocumentActivated -= DocumentManager_DocumentActivated;

                var doc = Application.DocumentManager.MdiActiveDocument;
                if (doc != null)
                {
                    doc.CommandWillStart -= Document_CommandWillStart;
                    doc.CommandEnded -= Document_CommandEnded;
                }

                _eventsRegistered = false;
                LogError("保护系统事件处理器已注销");
            }
            catch (System.Exception ex)
            {
                LogError($"清理保护系统失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 文档激活事件处理器
        /// </summary>
        private static void DocumentManager_DocumentActivated(object sender, DocumentCollectionEventArgs e)
        {
            try
            {
                if (e.Document?.Database != null)
                {
                    e.Document.Database.ObjectModified += Database_ObjectModified;
                    e.Document.Database.ObjectErased += Database_ObjectErased;
                    e.Document.Database.ObjectOpenedForModify += Database_ObjectOpenedForModify;
                }

                if (e.Document != null)
                {
                    e.Document.CommandWillStart += Document_CommandWillStart;
                    e.Document.CommandEnded += Document_CommandEnded;
                }
            }
            catch (System.Exception ex)
            {
                LogError($"文档激活事件处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 对象打开修改事件处理器 - 在对象被打开进行修改时触发
        /// </summary>
        private static void Database_ObjectOpenedForModify(object sender, ObjectEventArgs e)
        {
            try
            {
                if (IsEntityProtected(e.DBObject.ObjectId, out var protectionLevel, out var protectionUser))
                {
                    LogError($"阻止打开受保护的图元进行修改: {e.DBObject.ObjectId.Handle} (保护级别: {protectionLevel}, 保护用户: {protectionUser})");
                    ShowProtectionWarning($"图元已被保护，无法修改！保护用户: {protectionUser}");

                    // 尝试抛出异常来阻止操作
                    throw new Autodesk.AutoCAD.Runtime.Exception(ErrorStatus.ObjectIsReadOnly, "图元受保护，无法修改");
                }
            }
            catch (Autodesk.AutoCAD.Runtime.Exception)
            {
                // 重新抛出AutoCAD异常
                throw;
            }
            catch (System.Exception ex)
            {
                LogError($"对象打开修改事件处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 命令开始事件处理器
        /// </summary>
        private static void Document_CommandWillStart(object sender, CommandEventArgs e)
        {
            try
            {
                // 检查是否是可能修改图元的命令
                var dangerousCommands = new[] {
                    "MOVE", "COPY", "ROTATE", "SCALE", "STRETCH", "TRIM", "EXTEND",
                    "ERASE", "DELETE", "EXPLODE", "BREAK", "FILLET", "CHAMFER",
                    "MIRROR", "ARRAY", "OFFSET", "EDIT", "PEDIT", "SPLINEDIT",
                    "HATCHEDIT", "TEXTEDIT", "DDEDIT", "PROPERTIES", "MATCHPROP"
                };

                string cmdName = e.GlobalCommandName.ToUpper();
                if (dangerousCommands.Contains(cmdName))
                {
                    LogError($"检测到可能的编辑命令: {cmdName}");
                    // 这里可以添加额外的检查逻辑
                }
            }
            catch (System.Exception ex)
            {
                LogError($"命令开始事件处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 命令结束事件处理器
        /// </summary>
        private static void Document_CommandEnded(object sender, CommandEventArgs e)
        {
            try
            {
                // 命令结束后的清理工作
                LogError($"命令结束: {e.GlobalCommandName}");
            }
            catch (System.Exception ex)
            {
                LogError($"命令结束事件处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 对象修改事件处理器
        /// </summary>
        private static void Database_ObjectModified(object sender, ObjectEventArgs e)
        {
            try
            {
                if (IsEntityProtected(e.DBObject.ObjectId, out var protectionLevel, out var protectionUser))
                {
                    // 完全禁止修改，不管是谁
                    LogError($"阻止修改受保护的图元: {e.DBObject.ObjectId.Handle} (保护级别: {protectionLevel}, 保护用户: {protectionUser})");
                    ShowProtectionWarning($"图元已被保护，任何人都无法修改！保护用户: {protectionUser}");

                    // 尝试撤销修改 - 通过重新加载原始数据
                    try
                    {
                        RestoreProtectedEntity(e.DBObject.ObjectId);
                    }
                    catch (System.Exception restoreEx)
                    {
                        LogError($"恢复图元原始状态失败: {restoreEx.Message}");
                    }
                }
            }
            catch (System.Exception ex)
            {
                LogError($"对象修改事件处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 对象删除事件处理器
        /// </summary>
        private static void Database_ObjectErased(object sender, ObjectErasedEventArgs e)
        {
            try
            {
                if (e.Erased && IsEntityProtected(e.DBObject.ObjectId, out var protectionLevel, out var protectionUser))
                {
                    // 完全禁止删除，不管是谁
                    LogError($"阻止删除受保护的图元: {e.DBObject.ObjectId.Handle} (保护级别: {protectionLevel}, 保护用户: {protectionUser})");
                    ShowProtectionWarning($"图元已被保护，任何人都无法删除！保护用户: {protectionUser}");

                    // 尝试恢复被删除的对象
                    try
                    {
                        using (var tr = e.DBObject.Database.TransactionManager.StartTransaction())
                        {
                            var obj = tr.GetObject(e.DBObject.ObjectId, OpenMode.ForWrite);
                            obj.Erase(false); // 取消删除
                            tr.Commit();
                            LogError($"已恢复被保护的图元: {e.DBObject.ObjectId.Handle}");
                        }
                    }
                    catch (System.Exception restoreEx)
                    {
                        LogError($"恢复图元失败: {restoreEx.Message}");
                    }
                }
            }
            catch (System.Exception ex)
            {
                LogError($"对象删除事件处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 恢复受保护图元的原始状态
        /// </summary>
        private static void RestoreProtectedEntity(ObjectId entityId)
        {
            try
            {
                // 这里可以实现更复杂的恢复逻辑
                // 目前只是记录日志，因为在ObjectModified事件中很难完全撤销修改
                LogError($"尝试恢复图元 {entityId.Handle} 的原始状态");

                // 可以考虑以下方法：
                // 1. 保存图元的原始状态快照
                // 2. 在修改时恢复快照
                // 3. 或者设置图元为只读状态
            }
            catch (System.Exception ex)
            {
                LogError($"恢复图元状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查图元是否受保护
        /// </summary>
        public static bool IsEntityProtected(ObjectId entityId, out ProtectionLevel level, out string user)
        {
            level = ProtectionLevel.Full;
            user = string.Empty;

            try
            {
                if (entityId.IsNull || entityId.IsErased || !entityId.IsValid)
                    return false;

                using (var tr = entityId.Database.TransactionManager.StartTransaction())
                {
                    var entity = tr.GetObject(entityId, OpenMode.ForRead, false);
                    if (entity == null || entity.IsErased) return false;

                    if (entity.ExtensionDictionary.IsNull) return false;
                    var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForRead, false) as DBDictionary;
                    if (extDict == null || !extDict.Contains(PROTECTION_DICT_NAME)) return false;

                    var protectionDict = tr.GetObject(extDict.GetAt(PROTECTION_DICT_NAME), OpenMode.ForRead, false) as DBDictionary;
                    if (protectionDict == null) return false;

                    // 读取保护级别
                    if (protectionDict.Contains(PROTECTION_LEVEL_KEY))
                    {
                        var levelRec = tr.GetObject(protectionDict.GetAt(PROTECTION_LEVEL_KEY), OpenMode.ForRead, false) as Xrecord;
                        if (levelRec?.Data != null)
                        {
                            foreach (TypedValue tv in levelRec.Data)
                            {
                                if (tv.TypeCode == (int)DxfCode.Text && Enum.TryParse<ProtectionLevel>(tv.Value.ToString(), out level))
                                    break;
                            }
                        }
                    }

                    // 读取保护用户
                    if (protectionDict.Contains(PROTECTION_USER_KEY))
                    {
                        var userRec = tr.GetObject(protectionDict.GetAt(PROTECTION_USER_KEY), OpenMode.ForRead, false) as Xrecord;
                        if (userRec?.Data != null)
                        {
                            foreach (TypedValue tv in userRec.Data)
                            {
                                if (tv.TypeCode == (int)DxfCode.Text)
                                {
                                    user = tv.Value.ToString();
                                    break;
                                }
                            }
                        }
                    }

                    tr.Commit();
                    return true;
                }
            }
            catch (System.Exception ex)
            {
                LogError($"检查图元保护状态失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 显示保护警告
        /// </summary>
        private static void ShowProtectionWarning(string message)
        {
            try
            {
                var doc = Application.DocumentManager.MdiActiveDocument;
                if (doc?.Editor != null)
                {
                    doc.Editor.WriteMessage($"\n[保护警告] {message}");
                    
                    // 可以添加更明显的提示，比如对话框
                    // System.Windows.Forms.MessageBox.Show(message, "图元保护警告", 
                    //     System.Windows.Forms.MessageBoxButtons.OK, 
                    //     System.Windows.Forms.MessageBoxIcon.Warning);
                }
            }
            catch { }
        }

        /// <summary>
        /// 批量为所有有指定扩展记录的BlockReference加保护（支持强制更新）
        /// </summary>
        public static int ProtectEntitiesByXRecord(
            string recordName,
            ProtectionLevel level = ProtectionLevel.Full,
            string userName = null,
            bool forceUpdate = false)
        {
            // 确保保护系统已初始化
            InitializeProtectionSystem();

            var db = HostApplicationServices.WorkingDatabase;
            int successCount = 0;
            int alreadyProtectedCount = 0;

            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null) return 0;

            using (var docLock = doc.LockDocument())
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                    var ms = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                    foreach (ObjectId objId in ms)
                    {
                        try
                        {
                            if (objId.IsNull || objId.IsErased || !objId.IsValid) continue;

                            // 只处理BlockReference
                            var entity = tr.GetObject(objId, OpenMode.ForWrite, false) as BlockReference;
                            if (entity == null || entity.IsErased) continue;

                            // 必须有扩展字典
                            if (entity.ExtensionDictionary.IsNull) continue;
                            var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForWrite, false) as DBDictionary;
                            if (extDict == null) continue;

                            // 必须有指定的业务记录
                            if (!extDict.Contains(recordName)) continue;

                            // 检查是否已保护
                            bool isAlreadyProtected = extDict.Contains(PROTECTION_DICT_NAME);

                            if (isAlreadyProtected)
                            {
                                alreadyProtectedCount++;
                                if (forceUpdate)
                                {
                                    LogError($"强制更新已保护的图元: {objId.Handle} 类型:{entity.GetType().Name}");
                                    var protectionDict = tr.GetObject(extDict.GetAt(PROTECTION_DICT_NAME), OpenMode.ForWrite, false) as DBDictionary;
                                    if (protectionDict != null)
                                    {
                                        if (protectionDict.Contains(PROTECTION_LEVEL_KEY)) protectionDict.Remove(PROTECTION_LEVEL_KEY);
                                        if (protectionDict.Contains(PROTECTION_TIME_KEY)) protectionDict.Remove(PROTECTION_TIME_KEY);
                                        if (protectionDict.Contains(PROTECTION_USER_KEY)) protectionDict.Remove(PROTECTION_USER_KEY);
                                        AddProtectionData(protectionDict, tr, level, userName ?? Environment.UserName, false);

                                        // 确保图元为只读状态
                                        SetEntityReadOnly(entity, true);

                                        // 重新应用高级保护
                                        ApplyAdvancedProtection(objId);

                                        successCount++;
                                    }
                                }
                                else
                                {
                                    LogError($"跳过已保护的图元: {objId.Handle} 类型:{entity.GetType().Name}");
                                }
                            }
                            else
                            {
                                LogError($"保护新图元: {objId.Handle} 类型:{entity.GetType().Name}");
                                var protectionDict = new DBDictionary();
                                extDict.SetAt(PROTECTION_DICT_NAME, protectionDict);
                                tr.AddNewlyCreatedDBObject(protectionDict, true);
                                AddProtectionData(protectionDict, tr, level, userName ?? Environment.UserName);

                                // 设置图元为只读状态
                                SetEntityReadOnly(entity, true);

                                // 应用高级保护
                                ApplyAdvancedProtection(objId);

                                successCount++;
                            }
                        }
                        catch (System.Exception ex)
                        {
                            LogError($"处理实体 {objId.Handle} 时出错: {ex.Message} 类型:{objId.ObjectClass?.DxfName}");
                        }
                    }
                    tr.Commit();
                }
                catch (System.Exception ex)
                {
                    LogError($"批量保护操作失败: {ex.Message}");
                    tr.Abort();
                }
            }
            LogError($"处理完成: 新保护/更新了 {successCount} 个图元，跳过了 {alreadyProtectedCount} 个已保护的图元");
            return successCount;
        }

        /// <summary>
        /// 批量解除指定记录名的图元保护
        /// </summary>
        public static int UnprotectEntitiesByXRecord(string recordName)
        {
            var db = HostApplicationServices.WorkingDatabase;
            int successCount = 0;

            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null) return 0;

            using (var docLock = doc.LockDocument())
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                    var ms = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                    foreach (ObjectId objId in ms)
                    {
                        try
                        {
                            if (objId.IsNull || objId.IsErased || !objId.IsValid) continue;

                            var entity = tr.GetObject(objId, OpenMode.ForWrite, false) as BlockReference;
                            if (entity == null || entity.IsErased) continue;

                            if (entity.ExtensionDictionary.IsNull) continue;
                            var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForWrite, false) as DBDictionary;
                            if (extDict == null) continue;

                            // 必须有指定的业务记录
                            if (!extDict.Contains(recordName)) continue;

                            // 检查是否有保护记录
                            if (extDict.Contains(PROTECTION_DICT_NAME))
                            {
                                var protectionDict = tr.GetObject(extDict.GetAt(PROTECTION_DICT_NAME), OpenMode.ForWrite, false) as DBDictionary;
                                if (protectionDict != null)
                                {
                                    protectionDict.Erase();
                                    extDict.Remove(PROTECTION_DICT_NAME);

                                    // 移除高级保护
                                    RemoveAdvancedProtection(objId);

                                    // 恢复图元可编辑状态
                                    SetEntityReadOnly(entity, false);

                                    LogError($"解除保护: {objId.Handle} 类型:{entity.GetType().Name}");
                                    successCount++;
                                }
                            }
                        }
                        catch (System.Exception ex)
                        {
                            LogError($"解除保护实体 {objId.Handle} 时出错: {ex.Message}");
                        }
                    }
                    tr.Commit();
                }
                catch (System.Exception ex)
                {
                    LogError($"批量解除保护操作失败: {ex.Message}");
                    tr.Abort();
                }
            }
            LogError($"解除保护完成: 共解除了 {successCount} 个图元的保护");
            return successCount;
        }

        /// <summary>
        /// 统计指定记录名的图元保护状态
        /// </summary>
        public static void GetProtectionStatistics(string recordName)
        {
            var db = HostApplicationServices.WorkingDatabase;
            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null) return;

            int totalWithRecord = 0;
            int protectedCount = 0;
            int unprotectedCount = 0;

            using (var docLock = doc.LockDocument())
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                    var ms = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                    foreach (ObjectId objId in ms)
                    {
                        try
                        {
                            if (objId.IsNull || objId.IsErased || !objId.IsValid) continue;

                            var entity = tr.GetObject(objId, OpenMode.ForRead, false) as BlockReference;
                            if (entity == null || entity.IsErased) continue;

                            if (entity.ExtensionDictionary.IsNull) continue;
                            var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForRead, false) as DBDictionary;
                            if (extDict == null) continue;

                            if (!extDict.Contains(recordName)) continue;

                            totalWithRecord++;
                            if (extDict.Contains(PROTECTION_DICT_NAME))
                            {
                                protectedCount++;

                                // 获取保护详细信息
                                if (IsEntityProtected(objId, out var level, out var user))
                                {
                                    LogError($"已保护: {objId.Handle} 类型:{entity.GetType().Name} 级别:{level} 用户:{user}");
                                }
                                else
                                {
                                    LogError($"已保护: {objId.Handle} 类型:{entity.GetType().Name}");
                                }
                            }
                            else
                            {
                                unprotectedCount++;
                                LogError($"未保护: {objId.Handle} 类型:{entity.GetType().Name}");
                            }
                        }
                        catch (System.Exception ex)
                        {
                            LogError($"检查实体 {objId.Handle} 时出错: {ex.Message}");
                        }
                    }
                    tr.Commit();
                }
                catch (System.Exception ex)
                {
                    LogError($"统计操作失败: {ex.Message}");
                    tr.Abort();
                }
            }

            LogError($"统计结果: 总共 {totalWithRecord} 个{recordName}图元，其中 {protectedCount} 个已保护，{unprotectedCount} 个未保护");
        }

        /// <summary>
        /// 向保护字典添加保护数据
        /// </summary>
        private static void AddProtectionData(DBDictionary dict, Transaction tr, ProtectionLevel level, string user, bool addMainKey = true)
        {
            if (addMainKey)
            {
                var mainRec = new Xrecord { Data = new ResultBuffer(new TypedValue((int)DxfCode.Text, "PROTECTED")) };
                dict.SetAt(PROTECTION_DICT_NAME, mainRec);
                tr.AddNewlyCreatedDBObject(mainRec, true);
            }
            var levelRec = new Xrecord { Data = new ResultBuffer(new TypedValue((int)DxfCode.Text, level.ToString())) };
            dict.SetAt(PROTECTION_LEVEL_KEY, levelRec);
            tr.AddNewlyCreatedDBObject(levelRec, true);

            var timeRec = new Xrecord { Data = new ResultBuffer(new TypedValue((int)DxfCode.Text, DateTime.Now.ToString("o"))) };
            dict.SetAt(PROTECTION_TIME_KEY, timeRec);
            tr.AddNewlyCreatedDBObject(timeRec, true);

            var userRec = new Xrecord { Data = new ResultBuffer(new TypedValue((int)DxfCode.Text, user)) };
            dict.SetAt(PROTECTION_USER_KEY, userRec);
            tr.AddNewlyCreatedDBObject(userRec, true);
        }

        /// <summary>
        /// 设置图元的只读状态
        /// </summary>
        private static void SetEntityReadOnly(Entity entity, bool readOnly)
        {
            try
            {
                if (entity == null) return;

                // 方法1：尝试设置图层锁定（如果图元在特定图层）
                // 这种方法会影响整个图层，不太理想

                // 方法2：设置图元的可见性和选择性
                // entity.Visible = !readOnly; // 这会隐藏图元，不太好

                // 方法3：在扩展字典中添加特殊标记，配合事件处理
                // 这是我们当前使用的方法

                // 方法4：尝试设置图元的特殊属性
                if (readOnly)
                {
                    // 添加只读标记到扩展字典
                    if (!entity.ExtensionDictionary.IsNull)
                    {
                        using (var tr = entity.Database.TransactionManager.StartTransaction())
                        {
                            var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForWrite) as DBDictionary;
                            if (extDict != null && !extDict.Contains("READONLY_MARKER"))
                            {
                                var readOnlyRec = new Xrecord { Data = new ResultBuffer(new TypedValue((int)DxfCode.Text, "READONLY")) };
                                extDict.SetAt("READONLY_MARKER", readOnlyRec);
                                tr.AddNewlyCreatedDBObject(readOnlyRec, true);
                            }
                            tr.Commit();
                        }
                    }
                }

                LogError($"设置图元 {entity.ObjectId.Handle} 只读状态: {readOnly}");
            }
            catch (System.Exception ex)
            {
                LogError($"设置图元只读状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查图元是否为只读
        /// </summary>
        private static bool IsEntityReadOnly(ObjectId entityId)
        {
            try
            {
                if (entityId.IsNull || entityId.IsErased || !entityId.IsValid)
                    return false;

                using (var tr = entityId.Database.TransactionManager.StartTransaction())
                {
                    var entity = tr.GetObject(entityId, OpenMode.ForRead);
                    if (entity?.ExtensionDictionary.IsNull != false) return false;

                    var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForRead) as DBDictionary;
                    bool isReadOnly = extDict?.Contains("READONLY_MARKER") == true;

                    tr.Commit();
                    return isReadOnly;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 强制阻止图元编辑的高级方法
        /// </summary>
        public static void ApplyAdvancedProtection(ObjectId entityId)
        {
            try
            {
                using (var tr = entityId.Database.TransactionManager.StartTransaction())
                {
                    var entity = tr.GetObject(entityId, OpenMode.ForWrite) as Entity;
                    if (entity == null) return;

                    // 方法1：设置图元颜色为特殊颜色表示受保护
                    entity.ColorIndex = 8; // 深灰色表示受保护

                    // 方法2：添加多重保护标记
                    if (entity.ExtensionDictionary.IsNull)
                        entity.CreateExtensionDictionary();

                    var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForWrite) as DBDictionary;
                    if (extDict != null)
                    {
                        // 添加多个保护标记
                        var protectionMarkers = new[] { "LOCKED", "PROTECTED", "READONLY", "NO_EDIT", "FROZEN" };
                        foreach (var marker in protectionMarkers)
                        {
                            if (!extDict.Contains(marker))
                            {
                                var rec = new Xrecord { Data = new ResultBuffer(new TypedValue((int)DxfCode.Text, "PROTECTED")) };
                                extDict.SetAt(marker, rec);
                                tr.AddNewlyCreatedDBObject(rec, true);
                            }
                        }
                    }

                    tr.Commit();
                    LogError($"应用高级保护到图元: {entityId.Handle}");
                }
            }
            catch (System.Exception ex)
            {
                LogError($"应用高级保护失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 移除高级保护
        /// </summary>
        public static void RemoveAdvancedProtection(ObjectId entityId)
        {
            try
            {
                using (var tr = entityId.Database.TransactionManager.StartTransaction())
                {
                    var entity = tr.GetObject(entityId, OpenMode.ForWrite) as Entity;
                    if (entity == null) return;

                    // 恢复原始颜色（这里简单设置为白色，实际应该保存原始颜色）
                    entity.ColorIndex = 7; // 白色

                    // 移除保护标记
                    if (!entity.ExtensionDictionary.IsNull)
                    {
                        var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForWrite) as DBDictionary;
                        if (extDict != null)
                        {
                            var protectionMarkers = new[] { "LOCKED", "PROTECTED", "READONLY", "NO_EDIT", "FROZEN", "READONLY_MARKER" };
                            foreach (var marker in protectionMarkers)
                            {
                                if (extDict.Contains(marker))
                                {
                                    var rec = tr.GetObject(extDict.GetAt(marker), OpenMode.ForWrite);
                                    rec.Erase();
                                    extDict.Remove(marker);
                                }
                            }
                        }
                    }

                    tr.Commit();
                    LogError($"移除高级保护从图元: {entityId.Handle}");
                }
            }
            catch (System.Exception ex)
            {
                LogError($"移除高级保护失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 日志输出
        /// </summary>
        private static void LogError(string message)
        {
            try
            {
                Application.DocumentManager.MdiActiveDocument?.Editor?.WriteMessage($"\n[保护系统] {message}");
            }
            catch { }
        }
    }
}
