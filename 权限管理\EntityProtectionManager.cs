using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;

namespace SmartRouting.App.Commands.权限管理
{
    public static class EntityProtectionManager
    {
        private const string PROTECTION_DICT_NAME = "ENTITY_PROTECTION";
        private const string PROTECTION_LEVEL_KEY = "PROTECTION_LEVEL";
        private const string PROTECTION_TIME_KEY = "PROTECTION_TIME";
        private const string PROTECTION_USER_KEY = "PROTECTION_USER";

        public enum ProtectionLevel { Full, ReadOnly, Partial, DeleteOnly }

        private static bool _eventsRegistered = false;

        /// <summary>
        /// 初始化保护系统（注册事件处理器）
        /// </summary>
        public static void InitializeProtectionSystem()
        {
            if (_eventsRegistered) return;

            try
            {
                // 注册数据库事件
                var db = HostApplicationServices.WorkingDatabase;
                if (db != null)
                {
                    db.ObjectModified += Database_ObjectModified;
                    db.ObjectErased += Database_ObjectErased;
                    LogError("保护系统事件处理器已注册");
                }

                // 注册文档事件
                Application.DocumentManager.DocumentActivated += DocumentManager_DocumentActivated;
                
                _eventsRegistered = true;
            }
            catch (System.Exception ex)
            {
                LogError($"初始化保护系统失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理保护系统（注销事件处理器）
        /// </summary>
        public static void CleanupProtectionSystem()
        {
            if (!_eventsRegistered) return;

            try
            {
                var db = HostApplicationServices.WorkingDatabase;
                if (db != null)
                {
                    db.ObjectModified -= Database_ObjectModified;
                    db.ObjectErased -= Database_ObjectErased;
                }

                Application.DocumentManager.DocumentActivated -= DocumentManager_DocumentActivated;
                
                _eventsRegistered = false;
                LogError("保护系统事件处理器已注销");
            }
            catch (System.Exception ex)
            {
                LogError($"清理保护系统失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 文档激活事件处理器
        /// </summary>
        private static void DocumentManager_DocumentActivated(object sender, DocumentCollectionEventArgs e)
        {
            try
            {
                if (e.Document?.Database != null)
                {
                    e.Document.Database.ObjectModified += Database_ObjectModified;
                    e.Document.Database.ObjectErased += Database_ObjectErased;
                }
            }
            catch (System.Exception ex)
            {
                LogError($"文档激活事件处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 对象修改事件处理器
        /// </summary>
        private static void Database_ObjectModified(object sender, ObjectEventArgs e)
        {
            try
            {
                if (IsEntityProtected(e.DBObject.ObjectId, out var protectionLevel, out var protectionUser))
                {
                    var currentUser = Environment.UserName;
                    
                    // 检查是否是保护用户本人
                    if (string.Equals(protectionUser, currentUser, StringComparison.OrdinalIgnoreCase))
                    {
                        LogError($"保护用户 {currentUser} 修改了自己保护的图元: {e.DBObject.ObjectId.Handle}");
                        return; // 允许保护用户修改
                    }

                    // 根据保护级别决定是否阻止修改
                    switch (protectionLevel)
                    {
                        case ProtectionLevel.Full:
                        case ProtectionLevel.ReadOnly:
                            LogError($"阻止修改受保护的图元: {e.DBObject.ObjectId.Handle} (保护级别: {protectionLevel}, 保护用户: {protectionUser})");
                            // 注意：在ObjectModified事件中无法直接撤销操作，需要通过其他方式
                            ShowProtectionWarning($"图元已被用户 '{protectionUser}' 保护，无法修改！");
                            break;
                        case ProtectionLevel.Partial:
                            LogError($"部分保护的图元被修改: {e.DBObject.ObjectId.Handle}");
                            break;
                    }
                }
            }
            catch (System.Exception ex)
            {
                LogError($"对象修改事件处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 对象删除事件处理器
        /// </summary>
        private static void Database_ObjectErased(object sender, ObjectErasedEventArgs e)
        {
            try
            {
                if (e.Erased && IsEntityProtected(e.DBObject.ObjectId, out var protectionLevel, out var protectionUser))
                {
                    var currentUser = Environment.UserName;
                    
                    // 检查是否是保护用户本人
                    if (string.Equals(protectionUser, currentUser, StringComparison.OrdinalIgnoreCase))
                    {
                        LogError($"保护用户 {currentUser} 删除了自己保护的图元: {e.DBObject.ObjectId.Handle}");
                        return; // 允许保护用户删除
                    }

                    // 根据保护级别决定是否阻止删除
                    switch (protectionLevel)
                    {
                        case ProtectionLevel.Full:
                        case ProtectionLevel.ReadOnly:
                        case ProtectionLevel.Partial:
                            LogError($"阻止删除受保护的图元: {e.DBObject.ObjectId.Handle} (保护级别: {protectionLevel}, 保护用户: {protectionUser})");
                            ShowProtectionWarning($"图元已被用户 '{protectionUser}' 保护，无法删除！");
                            
                            // 尝试恢复被删除的对象
                            try
                            {
                                using (var tr = e.DBObject.Database.TransactionManager.StartTransaction())
                                {
                                    var obj = tr.GetObject(e.DBObject.ObjectId, OpenMode.ForWrite);
                                    obj.Erase(false); // 取消删除
                                    tr.Commit();
                                    LogError($"已恢复被保护的图元: {e.DBObject.ObjectId.Handle}");
                                }
                            }
                            catch (System.Exception restoreEx)
                            {
                                LogError($"恢复图元失败: {restoreEx.Message}");
                            }
                            break;
                        case ProtectionLevel.DeleteOnly:
                            LogError($"仅删除保护的图元被删除: {e.DBObject.ObjectId.Handle}");
                            break;
                    }
                }
            }
            catch (System.Exception ex)
            {
                LogError($"对象删除事件处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查图元是否受保护
        /// </summary>
        public static bool IsEntityProtected(ObjectId entityId, out ProtectionLevel level, out string user)
        {
            level = ProtectionLevel.Full;
            user = string.Empty;

            try
            {
                if (entityId.IsNull || entityId.IsErased || !entityId.IsValid)
                    return false;

                using (var tr = entityId.Database.TransactionManager.StartTransaction())
                {
                    var entity = tr.GetObject(entityId, OpenMode.ForRead, false);
                    if (entity == null || entity.IsErased) return false;

                    if (entity.ExtensionDictionary.IsNull) return false;
                    var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForRead, false) as DBDictionary;
                    if (extDict == null || !extDict.Contains(PROTECTION_DICT_NAME)) return false;

                    var protectionDict = tr.GetObject(extDict.GetAt(PROTECTION_DICT_NAME), OpenMode.ForRead, false) as DBDictionary;
                    if (protectionDict == null) return false;

                    // 读取保护级别
                    if (protectionDict.Contains(PROTECTION_LEVEL_KEY))
                    {
                        var levelRec = tr.GetObject(protectionDict.GetAt(PROTECTION_LEVEL_KEY), OpenMode.ForRead, false) as Xrecord;
                        if (levelRec?.Data != null)
                        {
                            foreach (TypedValue tv in levelRec.Data)
                            {
                                if (tv.TypeCode == (int)DxfCode.Text && Enum.TryParse<ProtectionLevel>(tv.Value.ToString(), out level))
                                    break;
                            }
                        }
                    }

                    // 读取保护用户
                    if (protectionDict.Contains(PROTECTION_USER_KEY))
                    {
                        var userRec = tr.GetObject(protectionDict.GetAt(PROTECTION_USER_KEY), OpenMode.ForRead, false) as Xrecord;
                        if (userRec?.Data != null)
                        {
                            foreach (TypedValue tv in userRec.Data)
                            {
                                if (tv.TypeCode == (int)DxfCode.Text)
                                {
                                    user = tv.Value.ToString();
                                    break;
                                }
                            }
                        }
                    }

                    tr.Commit();
                    return true;
                }
            }
            catch (System.Exception ex)
            {
                LogError($"检查图元保护状态失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 显示保护警告
        /// </summary>
        private static void ShowProtectionWarning(string message)
        {
            try
            {
                var doc = Application.DocumentManager.MdiActiveDocument;
                if (doc?.Editor != null)
                {
                    doc.Editor.WriteMessage($"\n[保护警告] {message}");
                    
                    // 可以添加更明显的提示，比如对话框
                    // System.Windows.Forms.MessageBox.Show(message, "图元保护警告", 
                    //     System.Windows.Forms.MessageBoxButtons.OK, 
                    //     System.Windows.Forms.MessageBoxIcon.Warning);
                }
            }
            catch { }
        }

        /// <summary>
        /// 批量为所有有指定扩展记录的BlockReference加保护（支持强制更新）
        /// </summary>
        public static int ProtectEntitiesByXRecord(
            string recordName,
            ProtectionLevel level = ProtectionLevel.Full,
            string userName = null,
            bool forceUpdate = false)
        {
            // 确保保护系统已初始化
            InitializeProtectionSystem();

            var db = HostApplicationServices.WorkingDatabase;
            int successCount = 0;
            int alreadyProtectedCount = 0;

            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null) return 0;

            using (var docLock = doc.LockDocument())
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                    var ms = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                    foreach (ObjectId objId in ms)
                    {
                        try
                        {
                            if (objId.IsNull || objId.IsErased || !objId.IsValid) continue;

                            // 只处理BlockReference
                            var entity = tr.GetObject(objId, OpenMode.ForWrite, false) as BlockReference;
                            if (entity == null || entity.IsErased) continue;

                            // 必须有扩展字典
                            if (entity.ExtensionDictionary.IsNull) continue;
                            var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForWrite, false) as DBDictionary;
                            if (extDict == null) continue;

                            // 必须有指定的业务记录
                            if (!extDict.Contains(recordName)) continue;

                            // 检查是否已保护
                            bool isAlreadyProtected = extDict.Contains(PROTECTION_DICT_NAME);

                            if (isAlreadyProtected)
                            {
                                alreadyProtectedCount++;
                                if (forceUpdate)
                                {
                                    LogError($"强制更新已保护的图元: {objId.Handle} 类型:{entity.GetType().Name}");
                                    var protectionDict = tr.GetObject(extDict.GetAt(PROTECTION_DICT_NAME), OpenMode.ForWrite, false) as DBDictionary;
                                    if (protectionDict != null)
                                    {
                                        if (protectionDict.Contains(PROTECTION_LEVEL_KEY)) protectionDict.Remove(PROTECTION_LEVEL_KEY);
                                        if (protectionDict.Contains(PROTECTION_TIME_KEY)) protectionDict.Remove(PROTECTION_TIME_KEY);
                                        if (protectionDict.Contains(PROTECTION_USER_KEY)) protectionDict.Remove(PROTECTION_USER_KEY);
                                        AddProtectionData(protectionDict, tr, level, userName ?? Environment.UserName, false);
                                        successCount++;
                                    }
                                }
                                else
                                {
                                    LogError($"跳过已保护的图元: {objId.Handle} 类型:{entity.GetType().Name}");
                                }
                            }
                            else
                            {
                                LogError($"保护新图元: {objId.Handle} 类型:{entity.GetType().Name}");
                                var protectionDict = new DBDictionary();
                                extDict.SetAt(PROTECTION_DICT_NAME, protectionDict);
                                tr.AddNewlyCreatedDBObject(protectionDict, true);
                                AddProtectionData(protectionDict, tr, level, userName ?? Environment.UserName);
                                successCount++;
                            }
                        }
                        catch (System.Exception ex)
                        {
                            LogError($"处理实体 {objId.Handle} 时出错: {ex.Message} 类型:{objId.ObjectClass?.DxfName}");
                        }
                    }
                    tr.Commit();
                }
                catch (System.Exception ex)
                {
                    LogError($"批量保护操作失败: {ex.Message}");
                    tr.Abort();
                }
            }
            LogError($"处理完成: 新保护/更新了 {successCount} 个图元，跳过了 {alreadyProtectedCount} 个已保护的图元");
            return successCount;
        }

        /// <summary>
        /// 批量解除指定记录名的图元保护
        /// </summary>
        public static int UnprotectEntitiesByXRecord(string recordName)
        {
            var db = HostApplicationServices.WorkingDatabase;
            int successCount = 0;

            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null) return 0;

            using (var docLock = doc.LockDocument())
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                    var ms = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                    foreach (ObjectId objId in ms)
                    {
                        try
                        {
                            if (objId.IsNull || objId.IsErased || !objId.IsValid) continue;

                            var entity = tr.GetObject(objId, OpenMode.ForWrite, false) as BlockReference;
                            if (entity == null || entity.IsErased) continue;

                            if (entity.ExtensionDictionary.IsNull) continue;
                            var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForWrite, false) as DBDictionary;
                            if (extDict == null) continue;

                            // 必须有指定的业务记录
                            if (!extDict.Contains(recordName)) continue;

                            // 检查是否有保护记录
                            if (extDict.Contains(PROTECTION_DICT_NAME))
                            {
                                var protectionDict = tr.GetObject(extDict.GetAt(PROTECTION_DICT_NAME), OpenMode.ForWrite, false) as DBDictionary;
                                if (protectionDict != null)
                                {
                                    protectionDict.Erase();
                                    extDict.Remove(PROTECTION_DICT_NAME);
                                    LogError($"解除保护: {objId.Handle} 类型:{entity.GetType().Name}");
                                    successCount++;
                                }
                            }
                        }
                        catch (System.Exception ex)
                        {
                            LogError($"解除保护实体 {objId.Handle} 时出错: {ex.Message}");
                        }
                    }
                    tr.Commit();
                }
                catch (System.Exception ex)
                {
                    LogError($"批量解除保护操作失败: {ex.Message}");
                    tr.Abort();
                }
            }
            LogError($"解除保护完成: 共解除了 {successCount} 个图元的保护");
            return successCount;
        }

        /// <summary>
        /// 统计指定记录名的图元保护状态
        /// </summary>
        public static void GetProtectionStatistics(string recordName)
        {
            var db = HostApplicationServices.WorkingDatabase;
            var doc = Application.DocumentManager.MdiActiveDocument;
            if (doc == null) return;

            int totalWithRecord = 0;
            int protectedCount = 0;
            int unprotectedCount = 0;

            using (var docLock = doc.LockDocument())
            using (var tr = db.TransactionManager.StartTransaction())
            {
                try
                {
                    var bt = (BlockTable)tr.GetObject(db.BlockTableId, OpenMode.ForRead);
                    var ms = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead);

                    foreach (ObjectId objId in ms)
                    {
                        try
                        {
                            if (objId.IsNull || objId.IsErased || !objId.IsValid) continue;

                            var entity = tr.GetObject(objId, OpenMode.ForRead, false) as BlockReference;
                            if (entity == null || entity.IsErased) continue;

                            if (entity.ExtensionDictionary.IsNull) continue;
                            var extDict = tr.GetObject(entity.ExtensionDictionary, OpenMode.ForRead, false) as DBDictionary;
                            if (extDict == null) continue;

                            if (!extDict.Contains(recordName)) continue;

                            totalWithRecord++;
                            if (extDict.Contains(PROTECTION_DICT_NAME))
                            {
                                protectedCount++;

                                // 获取保护详细信息
                                if (IsEntityProtected(objId, out var level, out var user))
                                {
                                    LogError($"已保护: {objId.Handle} 类型:{entity.GetType().Name} 级别:{level} 用户:{user}");
                                }
                                else
                                {
                                    LogError($"已保护: {objId.Handle} 类型:{entity.GetType().Name}");
                                }
                            }
                            else
                            {
                                unprotectedCount++;
                                LogError($"未保护: {objId.Handle} 类型:{entity.GetType().Name}");
                            }
                        }
                        catch (System.Exception ex)
                        {
                            LogError($"检查实体 {objId.Handle} 时出错: {ex.Message}");
                        }
                    }
                    tr.Commit();
                }
                catch (System.Exception ex)
                {
                    LogError($"统计操作失败: {ex.Message}");
                    tr.Abort();
                }
            }

            LogError($"统计结果: 总共 {totalWithRecord} 个{recordName}图元，其中 {protectedCount} 个已保护，{unprotectedCount} 个未保护");
        }

        /// <summary>
        /// 向保护字典添加保护数据
        /// </summary>
        private static void AddProtectionData(DBDictionary dict, Transaction tr, ProtectionLevel level, string user, bool addMainKey = true)
        {
            if (addMainKey)
            {
                var mainRec = new Xrecord { Data = new ResultBuffer(new TypedValue((int)DxfCode.Text, "PROTECTED")) };
                dict.SetAt(PROTECTION_DICT_NAME, mainRec);
                tr.AddNewlyCreatedDBObject(mainRec, true);
            }
            var levelRec = new Xrecord { Data = new ResultBuffer(new TypedValue((int)DxfCode.Text, level.ToString())) };
            dict.SetAt(PROTECTION_LEVEL_KEY, levelRec);
            tr.AddNewlyCreatedDBObject(levelRec, true);

            var timeRec = new Xrecord { Data = new ResultBuffer(new TypedValue((int)DxfCode.Text, DateTime.Now.ToString("o"))) };
            dict.SetAt(PROTECTION_TIME_KEY, timeRec);
            tr.AddNewlyCreatedDBObject(timeRec, true);

            var userRec = new Xrecord { Data = new ResultBuffer(new TypedValue((int)DxfCode.Text, user)) };
            dict.SetAt(PROTECTION_USER_KEY, userRec);
            tr.AddNewlyCreatedDBObject(userRec, true);
        }

        /// <summary>
        /// 日志输出
        /// </summary>
        private static void LogError(string message)
        {
            try
            {
                Application.DocumentManager.MdiActiveDocument?.Editor?.WriteMessage($"\n[保护系统] {message}");
            }
            catch { }
        }
    }
}
