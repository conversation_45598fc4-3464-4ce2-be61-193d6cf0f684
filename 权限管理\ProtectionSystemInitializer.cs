using System;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.Runtime;

namespace SmartRouting.App.Commands.权限管理
{
    /// <summary>
    /// 保护系统初始化器 - 在插件加载时自动初始化保护系统
    /// </summary>
    public class ProtectionSystemInitializer : IExtensionApplication
    {
        public void Initialize()
        {
            try
            {
                // 在插件加载时自动初始化保护系统
                EntityProtectionManager.InitializeProtectionSystem();
                
                // 输出初始化信息
                var doc = Application.DocumentManager.MdiActiveDocument;
                if (doc?.Editor != null)
                {
                    doc.Editor.WriteMessage("\n[保护系统] 图元保护系统已自动初始化");
                    doc.Editor.WriteMessage("\n[保护系统] 受保护的图元将无法被非保护用户修改或删除");
                }
            }
            catch (System.Exception ex)
            {
                // 如果初始化失败，记录错误但不影响插件加载
                var doc = Application.DocumentManager.MdiActiveDocument;
                if (doc?.Editor != null)
                {
                    doc.Editor.WriteMessage($"\n[保护系统] 初始化失败: {ex.Message}");
                }
            }
        }

        public void Terminate()
        {
            try
            {
                // 在插件卸载时清理保护系统
                EntityProtectionManager.CleanupProtectionSystem();
                
                var doc = Application.DocumentManager.MdiActiveDocument;
                if (doc?.Editor != null)
                {
                    doc.Editor.WriteMessage("\n[保护系统] 图元保护系统已清理");
                }
            }
            catch (System.Exception ex)
            {
                var doc = Application.DocumentManager.MdiActiveDocument;
                if (doc?.Editor != null)
                {
                    doc.Editor.WriteMessage($"\n[保护系统] 清理失败: {ex.Message}");
                }
            }
        }
    }
}
